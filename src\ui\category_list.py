# -*- coding: utf-8 -*-
"""
分类列表 - 左侧分类导航组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QListWidget, QListWidgetItem, 
                            QLabel, QFrame, QHBoxLayout, QPushButton)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from models.database import DatabaseManager
from utils.config_manager import ConfigManager


class CategoryList(QWidget):
    """分类列表组件"""
    
    # 信号
    category_changed = pyqtSignal(str)  # 分类改变信号
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        super().__init__()
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.current_category = "人物"  # 默认选中人物分类
        
        self.init_ui()
        self.load_categories()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("分类")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # 分类列表
        self.category_list = QListWidget()
        self.category_list.setAlternatingRowColors(True)
        self.category_list.itemClicked.connect(self.on_category_clicked)
        layout.addWidget(self.category_list)
        
        # 应用样式
        self.apply_styles()
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
            padding: 5px;
        }
        
        QListWidget {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            outline: none;
        }
        
        QListWidget::item {
            padding: 10px;
            border-bottom: 1px solid #4a4a4a;
            color: #ffffff;
        }
        
        QListWidget::item:selected {
            background-color: #5a5a5a;
        }
        
        QListWidget::item:hover {
            background-color: #4a4a4a;
        }
        
        QFrame {
            color: #5a5a5a;
        }
        """
        self.setStyleSheet(style)
    
    def load_categories(self):
        """加载分类列表"""
        categories = ["人物", "场景", "道具", "其他", "回收站"]
        
        for category in categories:
            item = QListWidgetItem(category)
            
            # 设置分类颜色
            color = self.config_manager.get_category_color(category)
            item.setData(Qt.ItemDataRole.UserRole, color)
            
            # 设置图标颜色（使用彩色圆点表示）
            self.set_item_color(item, color)
            
            self.category_list.addItem(item)
        
        # 默认选中第一个分类
        if self.category_list.count() > 0:
            self.category_list.setCurrentRow(0)
            self.category_changed.emit(self.current_category)
    
    def set_item_color(self, item: QListWidgetItem, color: str):
        """设置列表项颜色"""
        # 这里可以设置文本颜色或添加彩色图标
        # 暂时设置文本颜色
        qcolor = QColor(color)
        item.setForeground(qcolor)
    
    def on_category_clicked(self, item: QListWidgetItem):
        """分类点击事件"""
        category = item.text()
        if category != self.current_category:
            self.current_category = category
            self.category_changed.emit(category)
    
    def get_current_category(self) -> str:
        """获取当前选中的分类"""
        return self.current_category
    
    def set_category_color(self, category: str, color: str):
        """设置分类颜色"""
        self.config_manager.set_category_color(category, color)
        
        # 更新列表项颜色
        for i in range(self.category_list.count()):
            item = self.category_list.item(i)
            if item.text() == category:
                item.setData(Qt.ItemDataRole.UserRole, color)
                self.set_item_color(item, color)
                break
    
    def refresh_categories(self):
        """刷新分类列表"""
        self.category_list.clear()
        self.load_categories()
