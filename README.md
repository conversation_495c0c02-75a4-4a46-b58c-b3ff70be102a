# 简笔画素材管理软件

本项目采用 PyQt6 开发跨平台 GUI 应用，用于分类、管理简笔画图片、音视频等素材。

## 安装依赖

```bash
python -m pip install --upgrade pip
pip install -e .
```

> 依赖定义于 `pyproject.toml` 中，建议在 **Python 3.11** 虚拟环境中执行。

## 运行应用

```bash
python -m simple_sketch_manager
```

## 目录结构

```
简笔画素材管理/
├── pyproject.toml
├── README.md
└── src/
    └── simple_sketch_manager/
        ├── __init__.py
        ├── main.py
        ├── ui/
        │   ├── __init__.py
        │   └── main_window.py
        ├── models/
        │   ├── __init__.py
        │   └── database.py
        └── utils/
            └── __init__.py
```

## 许可证

MIT License 