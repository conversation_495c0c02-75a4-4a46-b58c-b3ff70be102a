# -*- coding: utf-8 -*-
"""
预览面板 - 右侧文件预览组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QScrollArea, QFrame, QPushButton, QTextEdit)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QPixmap, QFont

from utils.config_manager import ConfigManager
from pathlib import Path
import os


class PreviewPanel(QWidget):
    """预览面板组件"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.current_file = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("预览")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # 预览区域
        self.preview_scroll = QScrollArea()
        self.preview_scroll.setWidgetResizable(True)
        self.preview_scroll.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 预览内容容器
        self.preview_widget = QWidget()
        self.preview_layout = QVBoxLayout(self.preview_widget)
        self.preview_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 预览图片标签
        self.preview_image = QLabel()
        self.preview_image.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_image.setMinimumSize(200, 200)
        self.preview_image.setStyleSheet("""
            QLabel {
                border: 2px dashed #5a5a5a;
                border-radius: 5px;
                background-color: #3c3c3c;
                color: #888888;
            }
        """)
        self.preview_image.setText("选择文件以预览")
        self.preview_layout.addWidget(self.preview_image)
        
        self.preview_scroll.setWidget(self.preview_widget)
        layout.addWidget(self.preview_scroll)
        
        # 文件信息区域
        info_frame = QFrame()
        info_frame.setFrameShape(QFrame.Shape.Box)
        info_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #5a5a5a;
                border-radius: 5px;
                background-color: #3c3c3c;
                padding: 5px;
            }
        """)
        
        info_layout = QVBoxLayout(info_frame)
        
        # 文件信息标题
        info_title = QLabel("文件信息")
        info_title.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        info_layout.addWidget(info_title)
        
        # 文件信息内容
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(150)
        self.info_text.setReadOnly(True)
        self.info_text.setStyleSheet("""
            QTextEdit {
                border: none;
                background-color: transparent;
                color: #ffffff;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 9pt;
            }
        """)
        info_layout.addWidget(self.info_text)
        
        layout.addWidget(info_frame)
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        self.open_btn = QPushButton("打开")
        self.open_btn.clicked.connect(self.open_file)
        self.open_btn.setEnabled(False)
        button_layout.addWidget(self.open_btn)
        
        self.locate_btn = QPushButton("定位")
        self.locate_btn.clicked.connect(self.locate_file)
        self.locate_btn.setEnabled(False)
        button_layout.addWidget(self.locate_btn)
        
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
            padding: 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 15px;
            color: #ffffff;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #666666;
        }
        
        QScrollArea {
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            background-color: #3c3c3c;
        }
        
        QFrame {
            color: #5a5a5a;
        }
        """
        self.setStyleSheet(style)
    
    def show_file_preview(self, file_info: dict):
        """显示文件预览"""
        self.current_file = file_info
        
        # 更新预览图片
        self.update_preview_image(file_info)
        
        # 更新文件信息
        self.update_file_info(file_info)
        
        # 启用操作按钮
        self.open_btn.setEnabled(True)
        self.locate_btn.setEnabled(True)
    
    def update_preview_image(self, file_info: dict):
        """更新预览图片"""
        file_path = file_info.get('path', '')
        file_type = file_info.get('type', '').lower()
        
        if not file_path or not os.path.exists(file_path):
            self.preview_image.setText("文件不存在")
            return
        
        # 根据文件类型显示不同的预览
        if file_type in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
            self.show_image_preview(file_path)
        elif file_type in ['mp4', 'avi', 'mov', 'mkv', 'wmv']:
            self.show_video_preview(file_path)
        elif file_type in ['mp3', 'wav', 'flac', 'aac', 'ogg']:
            self.show_audio_preview(file_path)
        elif file_type in ['txt', 'md', 'py', 'js', 'html', 'css']:
            self.show_text_preview(file_path)
        else:
            self.show_default_preview(file_type)
    
    def show_image_preview(self, file_path: str):
        """显示图片预览"""
        try:
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                # 缩放图片以适应预览区域
                scaled_pixmap = pixmap.scaled(
                    self.preview_image.size() - QSize(20, 20),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                self.preview_image.setPixmap(scaled_pixmap)
                self.preview_image.setText("")
            else:
                self.preview_image.setText("无法加载图片")
        except Exception as e:
            self.preview_image.setText(f"加载失败: {str(e)}")
    
    def show_video_preview(self, file_path: str):
        """显示视频预览"""
        # 这里应该提取视频的关键帧作为预览
        # 暂时显示视频图标
        self.preview_image.setText("🎬\n视频文件")
        self.preview_image.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #3c3c3c;
                color: #ffffff;
                font-size: 32px;
            }
        """)
    
    def show_audio_preview(self, file_path: str):
        """显示音频预览"""
        # 显示音频图标
        self.preview_image.setText("🎵\n音频文件")
        self.preview_image.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #3c3c3c;
                color: #ffffff;
                font-size: 32px;
            }
        """)
    
    def show_text_preview(self, file_path: str):
        """显示文本预览"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(500)  # 只读取前500个字符
                if len(content) == 500:
                    content += "..."
                
                self.preview_image.setText(f"📄\n文本文件\n\n{content[:100]}...")
        except Exception:
            self.preview_image.setText("📄\n文本文件")
        
        self.preview_image.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #3c3c3c;
                color: #ffffff;
                font-size: 12px;
                padding: 10px;
            }
        """)
    
    def show_default_preview(self, file_type: str):
        """显示默认预览"""
        self.preview_image.setText(f"📄\n{file_type.upper()}\n文件")
        self.preview_image.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #3c3c3c;
                color: #ffffff;
                font-size: 24px;
            }
        """)
    
    def update_file_info(self, file_info: dict):
        """更新文件信息"""
        info_text = []
        
        # 基本信息
        info_text.append(f"文件名: {file_info.get('name', '未知')}")
        info_text.append(f"类型: {file_info.get('type', '未知').upper()}")
        info_text.append(f"分类: {file_info.get('category', '未知')}")
        
        # 路径信息
        file_path = file_info.get('path', '')
        if file_path:
            info_text.append(f"路径: {file_path}")
            
            # 文件大小
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                size_str = self.format_file_size(size)
                info_text.append(f"大小: {size_str}")
        
        # 时间信息
        if file_info.get('creation_time'):
            info_text.append(f"创建时间: {file_info['creation_time']}")
        
        if file_info.get('modified_time'):
            info_text.append(f"修改时间: {file_info['modified_time']}")
        
        # 自定义颜色
        if file_info.get('custom_color'):
            info_text.append(f"自定义颜色: {file_info['custom_color']}")
        
        self.info_text.setText('\n'.join(info_text))
    
    def format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    def open_file(self):
        """打开文件"""
        if self.current_file and self.current_file.get('path'):
            file_path = self.current_file['path']
            if os.path.exists(file_path):
                # 使用系统默认程序打开文件
                os.startfile(file_path)  # Windows
                # 对于其他系统，可以使用 subprocess.run(['open', file_path])  # macOS
                # 或 subprocess.run(['xdg-open', file_path])  # Linux
    
    def locate_file(self):
        """定位文件"""
        if self.current_file and self.current_file.get('path'):
            file_path = self.current_file['path']
            if os.path.exists(file_path):
                # 在文件管理器中显示文件
                os.system(f'explorer /select,"{file_path}"')  # Windows
                # 对于其他系统，可以使用相应的命令
    
    def clear_preview(self):
        """清除预览"""
        self.current_file = None
        self.preview_image.clear()
        self.preview_image.setText("选择文件以预览")
        self.preview_image.setStyleSheet("""
            QLabel {
                border: 2px dashed #5a5a5a;
                border-radius: 5px;
                background-color: #3c3c3c;
                color: #888888;
            }
        """)
        self.info_text.clear()
        self.open_btn.setEnabled(False)
        self.locate_btn.setEnabled(False)
