#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简笔画素材管理软件 - 主程序入口
作者：AI Assistant
版本：1.0.0
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from ui.main_window import MainWindow
from utils.config_manager import ConfigManager


def main():
    """主函数"""
    # 创建应用程序实例
    app = QApplication(sys.argv)
    app.setApplicationName("简笔画素材管理")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("简笔画工作室")
    
    # 设置应用程序属性（PyQt6中这些属性已被弃用，但保留兼容性检查）
    try:
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # PyQt6中这些属性已被移除，高DPI支持是默认启用的
        pass
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 创建主窗口
    main_window = MainWindow(config_manager)
    main_window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
