#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简笔画素材管理软件 - 启动脚本
这个脚本用于开发和测试阶段快速启动应用程序
"""

import sys
import os
from pathlib import Path

# 确保能够导入项目模块
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入主程序
from main import main

if __name__ == "__main__":
    print("正在启动简笔画素材管理软件...")
    print("项目根目录:", project_root)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
