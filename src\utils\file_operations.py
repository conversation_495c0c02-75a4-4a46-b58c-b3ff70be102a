# -*- coding: utf-8 -*-
"""
文件操作工具 - 处理文件导入、导出、移动等操作
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

from models.database import DatabaseManager
from utils.config_manager import ConfigManager


class FileOperations:
    """文件操作工具类"""
    
    # 支持的文件类型
    IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'}
    VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
    AUDIO_EXTENSIONS = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'}
    TEXT_EXTENSIONS = {'.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv'}
    
    # 分类文件类型限制
    CATEGORY_RESTRICTIONS = {
        '人物': IMAGE_EXTENSIONS,
        '场景': IMAGE_EXTENSIONS,
        '道具': IMAGE_EXTENSIONS,
        '其他': None,  # 无限制
        '回收站': None  # 无限制
    }
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.storage_path = config_manager.get_storage_path()
    
    def import_file(self, source_path: str, category: str, subfolder: str = "") -> bool:
        """
        导入文件到指定分类
        
        Args:
            source_path: 源文件路径
            category: 目标分类
            subfolder: 子文件夹（可选）
            
        Returns:
            bool: 导入是否成功
        """
        try:
            source_file = Path(source_path)
            
            # 检查源文件是否存在
            if not source_file.exists():
                print(f"源文件不存在: {source_path}")
                return False
            
            # 检查文件类型是否符合分类要求
            if not self._check_file_type_allowed(source_file, category):
                print(f"文件类型不符合分类 '{category}' 的要求")
                return False
            
            # 构建目标路径
            target_dir = self.storage_path / category
            if subfolder:
                target_dir = target_dir / subfolder
            
            # 确保目标目录存在
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成目标文件路径
            target_path = self._generate_unique_path(target_dir, source_file.name)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            
            # 添加到数据库
            file_type = source_file.suffix.lower().lstrip('.')
            self.db_manager.add_file(
                name=target_path.name,
                path=str(target_path),
                category=category,
                file_type=file_type
            )
            
            print(f"成功导入文件: {source_file.name} -> {target_path}")
            return True
            
        except Exception as e:
            print(f"导入文件失败: {e}")
            return False
    
    def import_files_batch(self, source_paths: List[str], category: str, 
                          subfolder: str = "") -> Tuple[int, int]:
        """
        批量导入文件
        
        Args:
            source_paths: 源文件路径列表
            category: 目标分类
            subfolder: 子文件夹（可选）
            
        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        success_count = 0
        failed_count = 0
        
        for source_path in source_paths:
            if self.import_file(source_path, category, subfolder):
                success_count += 1
            else:
                failed_count += 1
        
        return success_count, failed_count
    
    def export_files(self, file_ids: List[int], target_dir: str) -> bool:
        """
        导出文件到指定目录
        
        Args:
            file_ids: 要导出的文件ID列表
            target_dir: 目标目录
            
        Returns:
            bool: 导出是否成功
        """
        try:
            target_path = Path(target_dir)
            target_path.mkdir(parents=True, exist_ok=True)
            
            success_count = 0
            
            for file_id in file_ids:
                # 从数据库获取文件信息
                files = self.db_manager.get_files_by_category("")  # 获取所有文件
                file_info = None
                
                for file in files:
                    if file['id'] == file_id:
                        file_info = file
                        break
                
                if not file_info:
                    continue
                
                source_file = Path(file_info['path'])
                if not source_file.exists():
                    continue
                
                # 生成目标文件路径
                target_file = self._generate_unique_path(target_path, source_file.name)
                
                # 复制文件
                shutil.copy2(source_file, target_file)
                success_count += 1
            
            print(f"成功导出 {success_count} 个文件到 {target_dir}")
            return success_count > 0
            
        except Exception as e:
            print(f"导出文件失败: {e}")
            return False
    
    def move_file(self, file_id: int, target_category: str, target_subfolder: str = "") -> bool:
        """
        移动文件到新分类
        
        Args:
            file_id: 文件ID
            target_category: 目标分类
            target_subfolder: 目标子文件夹
            
        Returns:
            bool: 移动是否成功
        """
        try:
            # 获取文件信息
            files = self.db_manager.get_files_by_category("")  # 获取所有文件
            file_info = None
            
            for file in files:
                if file['id'] == file_id:
                    file_info = file
                    break
            
            if not file_info:
                return False
            
            source_path = Path(file_info['path'])
            if not source_path.exists():
                return False
            
            # 检查文件类型是否符合目标分类要求
            if not self._check_file_type_allowed(source_path, target_category):
                print(f"文件类型不符合分类 '{target_category}' 的要求")
                return False
            
            # 构建目标路径
            target_dir = self.storage_path / target_category
            if target_subfolder:
                target_dir = target_dir / target_subfolder
            
            target_dir.mkdir(parents=True, exist_ok=True)
            target_path = self._generate_unique_path(target_dir, source_path.name)
            
            # 移动文件
            shutil.move(str(source_path), str(target_path))
            
            # 更新数据库
            # 这里需要添加更新文件路径和分类的数据库方法
            # self.db_manager.update_file_location(file_id, str(target_path), target_category)
            
            return True
            
        except Exception as e:
            print(f"移动文件失败: {e}")
            return False
    
    def delete_file(self, file_id: int) -> bool:
        """
        删除文件（移至回收站）
        
        Args:
            file_id: 文件ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 获取文件信息
            files = self.db_manager.get_files_by_category("")  # 获取所有文件
            file_info = None
            
            for file in files:
                if file['id'] == file_id:
                    file_info = file
                    break
            
            if not file_info:
                return False
            
            source_path = Path(file_info['path'])
            if not source_path.exists():
                # 文件不存在，只更新数据库
                return self.db_manager.delete_file(file_id)
            
            # 移动到回收站目录
            recycle_dir = self.storage_path / "回收站"
            recycle_dir.mkdir(exist_ok=True)
            
            target_path = self._generate_unique_path(recycle_dir, source_path.name)
            shutil.move(str(source_path), str(target_path))
            
            # 更新数据库
            return self.db_manager.delete_file(file_id)
            
        except Exception as e:
            print(f"删除文件失败: {e}")
            return False
    
    def restore_file(self, file_id: int) -> bool:
        """
        从回收站还原文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            bool: 还原是否成功
        """
        try:
            # 获取文件信息
            deleted_files = self.db_manager.get_deleted_files()
            file_info = None
            
            for file in deleted_files:
                if file['id'] == file_id:
                    file_info = file
                    break
            
            if not file_info or not file_info.get('original_path'):
                return False
            
            current_path = Path(file_info['path'])
            original_path = Path(file_info['original_path'])
            
            if not current_path.exists():
                return False
            
            # 确保原始目录存在
            original_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 生成唯一的还原路径
            restore_path = self._generate_unique_path(original_path.parent, original_path.name)
            
            # 移动文件
            shutil.move(str(current_path), str(restore_path))
            
            # 更新数据库
            return self.db_manager.restore_file(file_id)
            
        except Exception as e:
            print(f"还原文件失败: {e}")
            return False
    
    def _check_file_type_allowed(self, file_path: Path, category: str) -> bool:
        """检查文件类型是否符合分类要求"""
        restrictions = self.CATEGORY_RESTRICTIONS.get(category)
        
        # 如果没有限制，允许所有类型
        if restrictions is None:
            return True
        
        file_ext = file_path.suffix.lower()
        return file_ext in restrictions
    
    def _generate_unique_path(self, directory: Path, filename: str) -> Path:
        """生成唯一的文件路径"""
        target_path = directory / filename
        
        if not target_path.exists():
            return target_path
        
        # 如果文件已存在，添加数字后缀
        name_part = target_path.stem
        ext_part = target_path.suffix
        counter = 1
        
        while target_path.exists():
            new_name = f"{name_part}_{counter}{ext_part}"
            target_path = directory / new_name
            counter += 1
        
        return target_path
    
    def get_file_hash(self, file_path: str) -> str:
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def find_duplicate_files(self, category: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """查找重复文件"""
        if category:
            files = self.db_manager.get_files_by_category(category)
        else:
            # 获取所有文件
            files = []
            categories = ["人物", "场景", "道具", "其他"]
            for cat in categories:
                files.extend(self.db_manager.get_files_by_category(cat))
        
        hash_groups = {}
        
        for file_info in files:
            file_path = file_info['path']
            if os.path.exists(file_path):
                file_hash = self.get_file_hash(file_path)
                if file_hash:
                    if file_hash not in hash_groups:
                        hash_groups[file_hash] = []
                    hash_groups[file_hash].append(file_info)
        
        # 只返回有重复的文件组
        duplicates = {h: files for h, files in hash_groups.items() if len(files) > 1}
        return duplicates
