# -*- coding: utf-8 -*-
"""
文件视图 - 中间文件显示区域组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QScrollArea, QLabel, QFrame, QPushButton, QFileDialog,
                            QMessageBox, QInputDialog, QMenu)
from PyQt6.QtCore import Qt, pyqtSignal, QSize, QMimeData, QUrl
from PyQt6.QtGui import QPixmap, QFont, QDragEnterEvent, QDropEvent, QContextMenuEvent

from models.database import DatabaseManager
from utils.config_manager import ConfigManager
from utils.file_operations import FileOperations
import os
from pathlib import Path


class FileItemWidget(QWidget):
    """文件项组件"""
    
    # 信号
    clicked = pyqtSignal(dict)  # 点击信号
    double_clicked = pyqtSignal(dict)  # 双击信号
    
    def __init__(self, file_info: dict, thumbnail_size: int = 100):
        super().__init__()
        self.file_info = file_info
        self.thumbnail_size = thumbnail_size
        self.selected = False
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 缩略图
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(self.thumbnail_size, self.thumbnail_size)
        self.thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #4a4a4a;
            }
        """)
        
        # 加载缩略图
        self.load_thumbnail()
        layout.addWidget(self.thumbnail_label)
        
        # 文件名
        self.name_label = QLabel(self.file_info.get('name', '未知'))
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setMaximumWidth(self.thumbnail_size + 10)
        self.name_label.setFont(QFont("Microsoft YaHei", 9))
        layout.addWidget(self.name_label)
        
        # 设置固定大小
        self.setFixedSize(self.thumbnail_size + 20, self.thumbnail_size + 50)
    
    def load_thumbnail(self):
        """加载缩略图"""
        # 这里应该根据文件类型加载不同的缩略图
        # 暂时显示占位图
        self.thumbnail_label.setText("📄")
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #4a4a4a;
                font-size: 32px;
            }
        """)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.file_info)
    
    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.double_clicked.emit(self.file_info)
    
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.selected = selected
        if selected:
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #0078d4;
                    border-radius: 5px;
                    background-color: #4a4a4a;
                    font-size: 32px;
                }
            """)
        else:
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #5a5a5a;
                    border-radius: 5px;
                    background-color: #4a4a4a;
                    font-size: 32px;
                }
            """)


class FileView(QWidget):
    """文件视图组件"""
    
    # 信号
    file_selected = pyqtSignal(dict)  # 文件选中信号
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        super().__init__()
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.file_operations = FileOperations(config_manager, db_manager)
        
        self.current_category = ""
        self.current_path = ""
        self.file_widgets = []
        self.selected_files = []
        
        self.init_ui()
        self.setup_drag_drop()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 路径导航栏
        nav_layout = QHBoxLayout()
        
        self.path_label = QLabel("当前位置: /")
        self.path_label.setFont(QFont("Microsoft YaHei", 10))
        nav_layout.addWidget(self.path_label)
        
        nav_layout.addStretch()
        
        # 视图切换按钮（预留）
        self.grid_view_btn = QPushButton("网格视图")
        self.grid_view_btn.setCheckable(True)
        self.grid_view_btn.setChecked(True)
        nav_layout.addWidget(self.grid_view_btn)
        
        layout.addLayout(nav_layout)
        
        # 文件显示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 文件容器
        self.file_container = QWidget()
        self.file_layout = QGridLayout(self.file_container)
        self.file_layout.setSpacing(10)
        
        self.scroll_area.setWidget(self.file_container)
        layout.addWidget(self.scroll_area)
        
        # 底部工具栏
        bottom_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all)
        bottom_layout.addWidget(self.select_all_btn)
        
        self.rename_btn = QPushButton("批量重命名")
        self.rename_btn.clicked.connect(self.batch_rename)
        bottom_layout.addWidget(self.rename_btn)
        
        self.delete_btn = QPushButton("删除选中")
        self.delete_btn.clicked.connect(self.delete_selected)
        bottom_layout.addWidget(self.delete_btn)
        
        self.color_btn = QPushButton("更改颜色")
        self.color_btn.clicked.connect(self.change_color)
        bottom_layout.addWidget(self.color_btn)
        
        bottom_layout.addStretch()
        
        layout.addLayout(bottom_layout)
        
        # 应用样式
        self.apply_styles()
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
            padding: 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px 10px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QScrollArea {
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            background-color: #3c3c3c;
        }
        """
        self.setStyleSheet(style)
    
    def setup_drag_drop(self):
        """设置拖放功能"""
        self.setAcceptDrops(True)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        """拖放事件"""
        urls = event.mimeData().urls()
        file_paths = [url.toLocalFile() for url in urls if url.isLocalFile()]
        
        if file_paths and self.current_category:
            self.import_files(file_paths)
    
    def set_category(self, category: str):
        """设置当前分类"""
        self.current_category = category
        self.current_path = f"/{category}"
        self.path_label.setText(f"当前位置: {self.current_path}")
        self.refresh_current_view()
    
    def refresh_current_view(self):
        """刷新当前视图"""
        # 清除现有文件组件
        self.clear_file_widgets()
        
        if not self.current_category:
            return
        
        # 获取文件列表
        files = self.db_manager.get_files_by_category(self.current_category)
        folders = self.db_manager.get_folders_by_category(self.current_category)
        
        # 显示文件和文件夹
        self.display_items(folders + files)
    
    def clear_file_widgets(self):
        """清除文件组件"""
        for widget in self.file_widgets:
            widget.deleteLater()
        self.file_widgets.clear()
        self.selected_files.clear()
    
    def display_items(self, items: list):
        """显示文件和文件夹"""
        columns = max(1, (self.scroll_area.width() - 50) // 130)  # 计算列数
        
        for i, item in enumerate(items):
            row = i // columns
            col = i % columns
            
            file_widget = FileItemWidget(item, self.config_manager.settings["thumbnail_size"])
            file_widget.clicked.connect(self.on_file_clicked)
            file_widget.double_clicked.connect(self.on_file_double_clicked)
            
            self.file_layout.addWidget(file_widget, row, col)
            self.file_widgets.append(file_widget)
    
    def on_file_clicked(self, file_info: dict):
        """文件点击事件"""
        # 清除其他选中状态
        for widget in self.file_widgets:
            widget.set_selected(False)
        
        # 设置当前选中
        sender = self.sender()
        if sender:
            sender.set_selected(True)
            self.selected_files = [file_info]
            self.file_selected.emit(file_info)
    
    def on_file_double_clicked(self, file_info: dict):
        """文件双击事件"""
        # 如果是文件夹，进入文件夹
        # 如果是文件，打开预览
        print(f"双击文件: {file_info}")
    
    def search_files(self, query: str):
        """搜索文件"""
        if not query.strip():
            self.refresh_current_view()
            return
        
        # 清除现有显示
        self.clear_file_widgets()
        
        # 搜索文件
        files = self.db_manager.search_files(query, self.current_category)
        self.display_items(files)
    
    def import_files(self, file_paths: list):
        """导入文件"""
        if not self.current_category:
            QMessageBox.warning(self, "警告", "请先选择一个分类")
            return
        
        success_count = 0
        for file_path in file_paths:
            if self.file_operations.import_file(file_path, self.current_category):
                success_count += 1
        
        if success_count > 0:
            QMessageBox.information(self, "导入完成", f"成功导入 {success_count} 个文件")
            self.refresh_current_view()
        else:
            QMessageBox.warning(self, "导入失败", "没有文件被成功导入")
    
    def select_all(self):
        """全选"""
        for widget in self.file_widgets:
            widget.set_selected(True)
        self.selected_files = [widget.file_info for widget in self.file_widgets]
    
    def rename_selected(self):
        """重命名选中项"""
        if len(self.selected_files) == 1:
            # 单个重命名
            file_info = self.selected_files[0]
            new_name, ok = QInputDialog.getText(self, "重命名", "新名称:", text=file_info['name'])
            if ok and new_name.strip():
                # 执行重命名操作
                print(f"重命名 {file_info['name']} 为 {new_name}")
        elif len(self.selected_files) > 1:
            # 批量重命名
            self.batch_rename()
    
    def batch_rename(self):
        """批量重命名"""
        if not self.selected_files:
            QMessageBox.information(self, "提示", "请先选择要重命名的文件")
            return
        
        # 这里应该打开批量重命名对话框
        QMessageBox.information(self, "提示", "批量重命名功能开发中...")
    
    def delete_selected(self):
        """删除选中文件"""
        if not self.selected_files:
            QMessageBox.information(self, "提示", "请先选择要删除的文件")
            return
        
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除选中的 {len(self.selected_files)} 个文件吗？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            for file_info in self.selected_files:
                self.db_manager.delete_file(file_info['id'])
            
            QMessageBox.information(self, "删除完成", "文件已移至回收站")
            self.refresh_current_view()
    
    def change_color(self):
        """更改颜色"""
        if not self.selected_files:
            QMessageBox.information(self, "提示", "请先选择要更改颜色的文件")
            return
        
        # 这里应该打开颜色选择对话框
        QMessageBox.information(self, "提示", "颜色更改功能开发中...")
    
    def contextMenuEvent(self, event: QContextMenuEvent):
        """右键菜单事件"""
        menu = QMenu(self)
        
        # 添加菜单项
        import_action = menu.addAction("导入文件")
        import_action.triggered.connect(self.show_import_dialog)
        
        if self.selected_files:
            menu.addSeparator()
            rename_action = menu.addAction("重命名")
            rename_action.triggered.connect(self.rename_selected)
            
            delete_action = menu.addAction("删除")
            delete_action.triggered.connect(self.delete_selected)
            
            color_action = menu.addAction("更改颜色")
            color_action.triggered.connect(self.change_color)
        
        menu.exec(event.globalPos())
    
    def show_import_dialog(self):
        """显示导入对话框"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择要导入的文件", "", 
            "所有文件 (*);;图片文件 (*.jpg *.jpeg *.png *.gif *.bmp);;视频文件 (*.mp4 *.avi *.mov *.mkv)"
        )
        
        if file_paths:
            self.import_files(file_paths)
