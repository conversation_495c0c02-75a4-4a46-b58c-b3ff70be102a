#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示设置脚本 - 创建一些示例文件用于测试
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from utils.config_manager import ConfigManager
from models.database import DatabaseManager
from utils.file_operations import FileOperations


def create_sample_images():
    """创建示例图片文件"""
    print("创建示例图片文件...")
    
    # 创建临时目录
    temp_dir = Path("temp_demo_files")
    temp_dir.mkdir(exist_ok=True)
    
    # 创建不同类型的示例图片
    sample_files = []
    
    # 人物图片
    for i, name in enumerate(["主角1", "主角2", "路人1", "怪兽1"], 1):
        img = Image.new('RGB', (200, 200), color=(100 + i*30, 150, 200))
        draw = ImageDraw.Draw(img)
        
        # 绘制简单的人物轮廓
        draw.ellipse([50, 30, 150, 130], fill=(255, 220, 177))  # 头部
        draw.rectangle([75, 130, 125, 180], fill=(100, 100, 255))  # 身体
        
        # 添加文字标签
        draw.text((10, 10), name, fill=(255, 255, 255))
        
        file_path = temp_dir / f"{name}.png"
        img.save(file_path)
        sample_files.append(("人物", str(file_path)))
    
    # 场景图片
    for i, name in enumerate(["室内场景1", "室外场景1"], 1):
        img = Image.new('RGB', (300, 200), color=(50 + i*50, 200, 100 + i*30))
        draw = ImageDraw.Draw(img)
        
        # 绘制简单场景
        if "室内" in name:
            draw.rectangle([20, 50, 280, 180], fill=(139, 69, 19))  # 地板
            draw.rectangle([50, 20, 250, 50], fill=(255, 255, 255))  # 天花板
        else:
            draw.ellipse([200, 20, 280, 100], fill=(255, 255, 0))  # 太阳
            draw.rectangle([0, 150, 300, 200], fill=(34, 139, 34))  # 草地
        
        draw.text((10, 10), name, fill=(255, 255, 255))
        
        file_path = temp_dir / f"{name}.png"
        img.save(file_path)
        sample_files.append(("场景", str(file_path)))
    
    # 道具图片
    for i, name in enumerate(["剑", "盾牌", "汽车"], 1):
        img = Image.new('RGB', (150, 150), color=(200, 100 + i*40, 100))
        draw = ImageDraw.Draw(img)
        
        # 绘制简单道具
        if name == "剑":
            draw.rectangle([70, 20, 80, 120], fill=(192, 192, 192))  # 剑身
            draw.rectangle([60, 120, 90, 130], fill=(139, 69, 19))   # 剑柄
        elif name == "盾牌":
            draw.ellipse([25, 25, 125, 125], fill=(139, 69, 19))     # 盾牌
        else:  # 汽车
            draw.rectangle([25, 75, 125, 100], fill=(255, 0, 0))     # 车身
            draw.ellipse([35, 100, 55, 120], fill=(0, 0, 0))        # 轮子
            draw.ellipse([95, 100, 115, 120], fill=(0, 0, 0))       # 轮子
        
        draw.text((10, 10), name, fill=(255, 255, 255))
        
        file_path = temp_dir / f"{name}.png"
        img.save(file_path)
        sample_files.append(("道具", str(file_path)))
    
    return sample_files


def create_sample_text_files():
    """创建示例文本文件"""
    print("创建示例文本文件...")
    
    temp_dir = Path("temp_demo_files")
    temp_dir.mkdir(exist_ok=True)
    
    sample_files = []
    
    # 创建文本文件
    text_files = {
        "故事大纲.txt": "这是一个关于勇者冒险的故事...\n第一章：出发\n第二章：遇到困难\n第三章：解决问题",
        "角色设定.md": "# 角色设定\n\n## 主角\n- 姓名：小明\n- 年龄：16岁\n- 特点：勇敢、善良\n\n## 反派\n- 姓名：黑暗法师\n- 特点：邪恶、强大",
        "脚本.py": "# 示例Python脚本\ndef main():\n    print('Hello, World!')\n\nif __name__ == '__main__':\n    main()"
    }
    
    for filename, content in text_files.items():
        file_path = temp_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        sample_files.append(("其他", str(file_path)))
    
    return sample_files


def import_sample_files():
    """导入示例文件到程序中"""
    print("初始化配置和数据库...")
    
    # 初始化配置管理器和数据库
    config_manager = ConfigManager()
    db_manager = DatabaseManager(str(config_manager.config_dir / "metadata.db"))
    file_operations = FileOperations(config_manager, db_manager)
    
    # 创建示例文件
    image_files = create_sample_images()
    text_files = create_sample_text_files()
    
    all_files = image_files + text_files
    
    print(f"开始导入 {len(all_files)} 个示例文件...")
    
    success_count = 0
    for category, file_path in all_files:
        if file_operations.import_file(file_path, category):
            success_count += 1
            print(f"  ✓ 导入成功: {Path(file_path).name} -> {category}")
        else:
            print(f"  ❌ 导入失败: {Path(file_path).name}")
    
    print(f"\n导入完成！成功导入 {success_count}/{len(all_files)} 个文件")
    
    # 清理临时文件
    print("清理临时文件...")
    temp_dir = Path("temp_demo_files")
    if temp_dir.exists():
        for file in temp_dir.iterdir():
            file.unlink()
        temp_dir.rmdir()
    
    print("演示数据设置完成！")
    return success_count > 0


def show_storage_info():
    """显示存储信息"""
    config_manager = ConfigManager()
    storage_path = config_manager.get_storage_path()
    
    print(f"\n存储路径: {storage_path}")
    print("分类目录结构:")
    
    for category in ["人物", "场景", "道具", "其他", "回收站"]:
        category_path = storage_path / category
        if category_path.exists():
            file_count = len(list(category_path.rglob("*.*")))
            print(f"  {category}: {file_count} 个文件")
            
            # 显示子文件夹
            for subfolder in category_path.iterdir():
                if subfolder.is_dir():
                    sub_file_count = len(list(subfolder.glob("*.*")))
                    print(f"    └── {subfolder.name}: {sub_file_count} 个文件")


if __name__ == "__main__":
    print("=" * 60)
    print("简笔画素材管理软件 - 演示数据设置")
    print("=" * 60)
    
    try:
        # 导入示例文件
        success = import_sample_files()
        
        if success:
            # 显示存储信息
            show_storage_info()
            
            print("\n" + "=" * 60)
            print("✅ 演示数据设置成功！")
            print("现在可以启动程序查看效果:")
            print("  python main.py")
            print("  或")
            print("  python run.py")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ 演示数据设置失败！")
            print("=" * 60)
            
    except Exception as e:
        print(f"\n❌ 设置过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
