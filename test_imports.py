#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证所有模块能否正常导入
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """测试所有模块的导入"""
    print("开始测试模块导入...")
    
    try:
        print("1. 测试PyQt6...")
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        print("   ✓ PyQt6导入成功")
        
        print("2. 测试配置管理器...")
        from utils.config_manager import ConfigManager
        print("   ✓ 配置管理器导入成功")
        
        print("3. 测试数据库管理器...")
        from models.database import DatabaseManager
        print("   ✓ 数据库管理器导入成功")
        
        print("4. 测试文件操作工具...")
        from utils.file_operations import FileOperations
        print("   ✓ 文件操作工具导入成功")
        
        print("5. 测试UI组件...")
        from ui.main_window import MainWindow
        from ui.category_list import CategoryList
        from ui.file_view import FileView
        from ui.preview_panel import PreviewPanel
        from ui.mini_window import MiniWindow
        print("   ✓ 所有UI组件导入成功")

        print("6. 测试新增工具...")
        from utils.thumbnail import ThumbnailGenerator
        from utils.navigation import NavigationManager
        print("   ✓ 缩略图生成器和导航管理器导入成功")
        
        print("\n🎉 所有模块导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n开始测试基本功能...")
    
    try:
        # 测试配置管理器
        print("1. 测试配置管理器...")
        from utils.config_manager import ConfigManager
        config = ConfigManager()
        print(f"   ✓ 配置目录: {config.config_dir}")
        print(f"   ✓ 存储路径: {config.get_storage_path()}")
        
        # 测试数据库管理器
        print("2. 测试数据库管理器...")
        from models.database import DatabaseManager
        db = DatabaseManager(str(config.config_dir / "test.db"))
        print("   ✓ 数据库初始化成功")
        
        # 测试文件操作
        print("3. 测试文件操作工具...")
        from utils.file_operations import FileOperations
        file_ops = FileOperations(config, db)
        print("   ✓ 文件操作工具初始化成功")
        
        print("\n🎉 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"   ❌ 功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("简笔画素材管理软件 - 模块测试")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试基本功能
        func_success = test_basic_functionality()
        
        if func_success:
            print("\n" + "=" * 50)
            print("✅ 所有测试通过！项目初始化成功！")
            print("可以使用以下命令启动程序:")
            print("  python main.py")
            print("  或")
            print("  python run.py")
            print("=" * 50)
        else:
            print("\n" + "=" * 50)
            print("❌ 功能测试失败，请检查代码")
            print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("❌ 模块导入失败，请检查依赖安装")
        print("=" * 50)
