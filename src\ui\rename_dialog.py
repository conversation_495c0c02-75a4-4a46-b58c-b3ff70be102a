# -*- coding: utf-8 -*-
"""
重命名对话框 - 文件和文件夹重命名功能
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Any, Optional

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTextEdit, QTabWidget,
                            QWidget, QFormLayout, QSpinBox, QCheckBox,
                            QComboBox, QMessageBox, QProgressBar)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from utils.config_manager import ConfigManager
from models.database import DatabaseManager


class RenameDialog(QDialog):
    """重命名对话框"""
    
    # 信号
    files_renamed = pyqtSignal(list)  # 文件重命名完成信号
    
    def __init__(self, files: List[Dict[str, Any]], config_manager: Config<PERSON><PERSON><PERSON>, 
                 db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.files = files
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.is_batch = len(files) > 1
        
        self.init_ui()
        self.setup_connections()
        
        # 如果是单个文件，显示当前名称
        if not self.is_batch:
            current_name = self.files[0].get('name', '')
            # 去掉扩展名（如果是文件）
            if not self.files[0].get('is_folder', False):
                current_name = Path(current_name).stem
            self.single_name_edit.setText(current_name)
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("重命名" if not self.is_batch else "批量重命名")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        if self.is_batch:
            self.init_batch_ui(layout)
        else:
            self.init_single_ui(layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("预览")
        self.preview_btn.clicked.connect(self.show_preview)
        button_layout.addWidget(self.preview_btn)
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept_rename)
        self.ok_btn.setDefault(True)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def init_single_ui(self, layout: QVBoxLayout):
        """初始化单个文件重命名UI"""
        # 文件信息
        file_info = self.files[0]
        info_label = QLabel(f"重命名: {file_info.get('name', '')}")
        info_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        layout.addWidget(info_label)
        
        # 输入框
        form_layout = QFormLayout()
        
        self.single_name_edit = QLineEdit()
        self.single_name_edit.setPlaceholderText("输入新名称...")
        form_layout.addRow("新名称:", self.single_name_edit)
        
        layout.addLayout(form_layout)
        layout.addStretch()
    
    def init_batch_ui(self, layout: QVBoxLayout):
        """初始化批量重命名UI"""
        # 标签页
        tab_widget = QTabWidget()
        
        # 基础重命名标签页
        basic_tab = QWidget()
        basic_layout = QFormLayout(basic_tab)
        
        self.base_name_edit = QLineEdit()
        self.base_name_edit.setPlaceholderText("基础名称...")
        basic_layout.addRow("基础名称:", self.base_name_edit)
        
        self.start_number_spin = QSpinBox()
        self.start_number_spin.setMinimum(0)
        self.start_number_spin.setMaximum(9999)
        self.start_number_spin.setValue(1)
        basic_layout.addRow("起始编号:", self.start_number_spin)
        
        self.number_digits_spin = QSpinBox()
        self.number_digits_spin.setMinimum(1)
        self.number_digits_spin.setMaximum(6)
        self.number_digits_spin.setValue(2)
        basic_layout.addRow("编号位数:", self.number_digits_spin)
        
        tab_widget.addTab(basic_tab, "基础重命名")
        
        # 人物分类特殊重命名
        if self.files and self.files[0].get('category') == '人物':
            character_tab = QWidget()
            char_layout = QFormLayout(character_tab)
            
            self.char_name_edit = QLineEdit()
            self.char_name_edit.setPlaceholderText("角色名称...")
            char_layout.addRow("角色名称:", self.char_name_edit)
            
            self.expression_edit = QLineEdit()
            self.expression_edit.setPlaceholderText("表情（可选）...")
            char_layout.addRow("表情:", self.expression_edit)
            
            self.action_edit = QLineEdit()
            self.action_edit.setPlaceholderText("动作（可选）...")
            char_layout.addRow("动作:", self.action_edit)
            
            tab_widget.addTab(character_tab, "人物重命名")
        
        # 高级重命名标签页
        advanced_tab = QWidget()
        adv_layout = QFormLayout(advanced_tab)
        
        self.pattern_edit = QLineEdit()
        self.pattern_edit.setPlaceholderText("重命名模式，如: {name}_{index}")
        adv_layout.addRow("重命名模式:", self.pattern_edit)
        
        self.replace_spaces_check = QCheckBox("将空格替换为下划线")
        adv_layout.addRow("", self.replace_spaces_check)
        
        self.lowercase_check = QCheckBox("转换为小写")
        adv_layout.addRow("", self.lowercase_check)
        
        tab_widget.addTab(advanced_tab, "高级重命名")
        
        layout.addWidget(tab_widget)
        
        # 预览区域
        preview_label = QLabel("预览:")
        preview_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        layout.addWidget(preview_label)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setReadOnly(True)
        layout.addWidget(self.preview_text)
    
    def setup_connections(self):
        """设置信号连接"""
        if self.is_batch:
            self.base_name_edit.textChanged.connect(self.update_preview)
            self.start_number_spin.valueChanged.connect(self.update_preview)
            self.number_digits_spin.valueChanged.connect(self.update_preview)
            self.pattern_edit.textChanged.connect(self.update_preview)
            
            if hasattr(self, 'char_name_edit'):
                self.char_name_edit.textChanged.connect(self.update_preview)
                self.expression_edit.textChanged.connect(self.update_preview)
                self.action_edit.textChanged.connect(self.update_preview)
    
    def update_preview(self):
        """更新预览"""
        if not self.is_batch:
            return
        
        try:
            new_names = self.generate_new_names()
            preview_text = []
            
            for i, (old_file, new_name) in enumerate(zip(self.files, new_names)):
                old_name = old_file.get('name', '')
                preview_text.append(f"{i+1:2d}. {old_name} → {new_name}")
            
            self.preview_text.setPlainText('\n'.join(preview_text))
        except Exception as e:
            self.preview_text.setPlainText(f"预览错误: {str(e)}")
    
    def generate_new_names(self) -> List[str]:
        """生成新的文件名列表"""
        if not self.is_batch:
            new_name = self.single_name_edit.text().strip()
            if not new_name:
                return []
            
            # 保留原始扩展名
            file_info = self.files[0]
            if not file_info.get('is_folder', False):
                original_name = file_info.get('name', '')
                ext = Path(original_name).suffix
                new_name += ext
            
            return [new_name]
        
        new_names = []
        
        # 检查是否使用人物重命名模式
        if (hasattr(self, 'char_name_edit') and 
            self.char_name_edit.text().strip()):
            new_names = self._generate_character_names()
        elif self.pattern_edit.text().strip():
            new_names = self._generate_pattern_names()
        else:
            new_names = self._generate_basic_names()
        
        return new_names
    
    def _generate_basic_names(self) -> List[str]:
        """生成基础重命名"""
        base_name = self.base_name_edit.text().strip() or "文件"
        start_num = self.start_number_spin.value()
        digits = self.number_digits_spin.value()
        
        new_names = []
        for i, file_info in enumerate(self.files):
            number = str(start_num + i).zfill(digits)
            
            # 保留原始扩展名
            if not file_info.get('is_folder', False):
                original_name = file_info.get('name', '')
                ext = Path(original_name).suffix
                new_name = f"{base_name}{number}{ext}"
            else:
                new_name = f"{base_name}{number}"
            
            new_names.append(new_name)
        
        return new_names
    
    def _generate_character_names(self) -> List[str]:
        """生成人物重命名"""
        char_name = self.char_name_edit.text().strip()
        expression = self.expression_edit.text().strip()
        action = self.action_edit.text().strip()
        
        new_names = []
        for i, file_info in enumerate(self.files):
            name_parts = [char_name]
            
            if expression:
                name_parts.append(expression)
            
            if action:
                name_parts.append(action)
            
            # 如果有多个文件，添加编号
            if len(self.files) > 1:
                name_parts.append(str(i + 1))
            
            new_name = '-'.join(name_parts)
            
            # 保留原始扩展名
            if not file_info.get('is_folder', False):
                original_name = file_info.get('name', '')
                ext = Path(original_name).suffix
                new_name += ext
            
            new_names.append(new_name)
        
        return new_names
    
    def _generate_pattern_names(self) -> List[str]:
        """生成模式重命名"""
        pattern = self.pattern_edit.text().strip()
        
        new_names = []
        for i, file_info in enumerate(self.files):
            # 替换模式变量
            new_name = pattern.replace('{index}', str(i + 1))
            new_name = new_name.replace('{name}', Path(file_info.get('name', '')).stem)
            new_name = new_name.replace('{category}', file_info.get('category', ''))
            
            # 应用选项
            if self.replace_spaces_check.isChecked():
                new_name = new_name.replace(' ', '_')
            
            if self.lowercase_check.isChecked():
                new_name = new_name.lower()
            
            # 保留原始扩展名
            if not file_info.get('is_folder', False):
                original_name = file_info.get('name', '')
                ext = Path(original_name).suffix
                new_name += ext
            
            new_names.append(new_name)
        
        return new_names
    
    def show_preview(self):
        """显示预览"""
        if self.is_batch:
            self.update_preview()
        else:
            new_names = self.generate_new_names()
            if new_names:
                old_name = self.files[0].get('name', '')
                QMessageBox.information(self, "预览", f"原名称: {old_name}\n新名称: {new_names[0]}")
    
    def accept_rename(self):
        """确认重命名"""
        try:
            new_names = self.generate_new_names()
            
            if not new_names:
                QMessageBox.warning(self, "错误", "请输入有效的文件名")
                return
            
            # 检查名称冲突
            conflicts = self._check_name_conflicts(new_names)
            if conflicts:
                QMessageBox.warning(self, "名称冲突", f"以下名称已存在:\n{chr(10).join(conflicts)}")
                return
            
            # 执行重命名
            success_count = self._perform_rename(new_names)
            
            if success_count > 0:
                self.files_renamed.emit(self.files)
                QMessageBox.information(self, "完成", f"成功重命名 {success_count} 个文件")
                self.accept()
            else:
                QMessageBox.warning(self, "失败", "重命名失败")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"重命名过程中出现错误: {str(e)}")
    
    def _check_name_conflicts(self, new_names: List[str]) -> List[str]:
        """检查名称冲突"""
        conflicts = []
        
        for file_info, new_name in zip(self.files, new_names):
            file_path = Path(file_info.get('path', ''))
            new_path = file_path.parent / new_name
            
            if new_path.exists() and str(new_path) != file_info.get('path', ''):
                conflicts.append(new_name)
        
        return conflicts
    
    def _perform_rename(self, new_names: List[str]) -> int:
        """执行重命名操作"""
        success_count = 0
        
        for file_info, new_name in zip(self.files, new_names):
            try:
                old_path = Path(file_info.get('path', ''))
                new_path = old_path.parent / new_name
                
                # 重命名文件
                old_path.rename(new_path)
                
                # 更新数据库（如果有ID）
                file_id = file_info.get('id', 0)
                if file_id > 0:
                    # 这里需要添加数据库更新方法
                    pass
                
                success_count += 1
                
            except Exception as e:
                print(f"重命名失败 {file_info.get('name', '')}: {e}")
        
        return success_count
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
        }
        
        QLineEdit, QSpinBox, QTextEdit {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 15px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QTabWidget::pane {
            border: 1px solid #5a5a5a;
            background-color: #3c3c3c;
        }
        
        QTabBar::tab {
            background-color: #4a4a4a;
            color: #ffffff;
            padding: 8px 15px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #5a5a5a;
        }
        """
        self.setStyleSheet(style)
